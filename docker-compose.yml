services:
  postgres:
    image: postgres:17-alpine
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./db_init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - app_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER}"]
      interval: 100s
      timeout: 5s
      retries: 5

  auth-backend:
    image: ekinyua/wildpedia-backend:latest
    environment:
      - NODE_ENV=development
      - PORT=5000
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USER=${POSTGRES_USER}
      - DB_PASSWORD=${POSTGRES_PASSWORD}
      - DB_NAME=${POSTGRES_DB}
      - JWT_SECRET=${JWT_SECRET}
      - SMTP_HOST=${SMTP_HOST}
      - SMTP_PORT=${SMTP_PORT}
      - SMTP_USER=${SMTP_USER}
      - SMTP_PASS=${SMTP_PASS}
      - SMTP_FROM=${SMTP_FROM}
      - FRONTEND_URL=${FRONTEND_URL}
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      - GOOGLE_CALLBACK_URL=${GOOGLE_CALLBACK_URL}
      - SESSION_SECRET=${SESSION_SECRET}
    ports:
      - "5000:5000"
    depends_on:
      postgres:
        condition: service_started
    volumes:
      - ./backend:/app
      - /app/node_modules
      - ./backend/neuralninevisionproject-449711-aa07c7cc5bae.json:/app/neuralninevisionproject-449711-aa07c7cc5bae.json
    networks:
      - app_network
    restart: unless-stopped

  quiz-service:
    image: ekinyua/wildpedia-quiz-service:latest
    environment:
      - NODE_ENV=development
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - TAVILY_API_KEY=${TAVILY_API_KEY}
    ports:
      - "3000:3000"
    volumes:
      - ./AI_quiz_generator:/app
      - /app/node_modules
    networks:
      - app_network
    restart: unless-stopped

  frontend:
    image: ekinyua/wildpedia-frontend:latest
    ports:
      - "5173:5173"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - VITE_API_URL=http://localhost:5000
      - VITE_QUIZ_API_URL=http://localhost:3000
    depends_on:
      - auth-backend
      - quiz-service
    networks:
      - app_network
    restart: unless-stopped

networks:
  app_network:
    driver: bridge

volumes:
  postgres_data:
  db_init_data:
