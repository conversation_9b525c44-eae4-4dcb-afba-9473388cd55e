require('dotenv').config();
const express = require('express');
const cors = require("cors");
const ipapi = require('ipapi.co');
const pool = require('./models/db');
const logger = require("./logger");
const passport = require('./config/passport');
const session = require('express-session');

// Route imports
const adminRoutes = require('./routes/adminRoutes');
const authRoutes = require('./routes/auth.routes');
const userRoutes = require('./routes/user.routes');
const culturalContentRoutes = require('./routes/cultural_content.routes');
const speciesFactsRoutes = require('./routes/species_facts.routes');
const { verifyToken } = require('./middleware/auth.middleware');

const app = express();

const path = require('path');

app.use(cors({
    origin: process.env.FRONTEND_URL || 'http://localhost:5173',
    credentials: true
}));
app.use(express.json());

app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Session configuration
app.use(session({
    secret: process.env.SESSION_SECRET || 'session-secret',
    resave: false,
    saveUninitialized: false,
    cookie: { secure: process.env.NODE_ENV === 'production' }
}));

// Initialize Passport
app.use(passport.initialize());

// Log application start
logger.info("Application started successfully.");

const visionRoutes = require('./routes/vision.routes');
const userStatsRoutes = require('./routes/user_stats.routes');

// Mounting all route groups
app.use('/api/admin/species', adminRoutes);
app.use('/api/admin', require('./routes/admin.routes'));
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/cultural-content', culturalContentRoutes);
app.use('/api/species-facts', speciesFactsRoutes);
app.use('/api/vision', visionRoutes);
app.use('/api/stats', userStatsRoutes);

// Protected test route
app.use('/api/protected', verifyToken, (req, res) => {
    res.json({ message: "This is a protected route", user: req.user });
});

// Route to fetch species based on search query or get all species
app.get("/api/species", async (req, res) => {
    try {
        let location = req.query.location || 'Kigali';
        let searchQuery = req.query.q || '';
        // Add pagination parameters
        const limit = parseInt(req.query.limit) || 12;
        const offset = parseInt(req.query.offset) || 0;

        let query;
        let values;

        if (searchQuery) {
            // Search query with completeness filter
            query = `
                SELECT *
                FROM species
                WHERE (scientific_name ILIKE $1 OR vernacular_names::text ILIKE $1)
                AND image_url IS NOT NULL 
                AND description IS NOT NULL 
                AND scientific_name IS NOT NULL
                AND array_length(vernacular_names, 1) > 0
                ORDER BY scientific_name
                LIMIT $2 OFFSET $3
            `;
            values = [`%${searchQuery}%`, limit, offset];
        } else {
            // Default query with completeness filter - Parameter indices corrected
            query = `
                SELECT *
                FROM species
                WHERE image_url IS NOT NULL 
                AND description IS NOT NULL 
                AND scientific_name IS NOT NULL
                AND array_length(vernacular_names, 1) > 0
                ORDER BY scientific_name
                LIMIT $1 OFFSET $2
            `;
            values = [limit, offset]; // Only two parameters here
        }

        // Fix count query as well
        let countQuery;
        let countValues;

        if (searchQuery) {
            countQuery = `
                SELECT COUNT(*) as total
                FROM species
                WHERE image_url IS NOT NULL 
                AND description IS NOT NULL 
                AND scientific_name IS NOT NULL
                AND array_length(vernacular_names, 1) > 0
                AND (scientific_name ILIKE $1 OR vernacular_names::text ILIKE $1)
            `;
            countValues = [`%${searchQuery}%`];
        } else {
            countQuery = `
                SELECT COUNT(*) as total
                FROM species
                WHERE image_url IS NOT NULL 
                AND description IS NOT NULL 
                AND scientific_name IS NOT NULL
                AND array_length(vernacular_names, 1) > 0
            `;
            countValues = [];
        }

        // Execute both queries
        const result = await pool.query(query, values);
        const countResult = await pool.query(countQuery, countValues);
        const totalCount = parseInt(countResult.rows[0].total);

        // Format the response - rest of your code remains the same
        const formattedResults = result.rows.map(species => ({
            key: species.gbif_key,
            scientificName: species.scientific_name,
            vernacularNames: species.vernacular_names || [],
            rank: species.taxonomic_status,
            kingdom: species.kingdom,
            phylum: species.phylum,
            family: species.family,
            image: species.image_url || 'placeholder.jpg',
            compositeKey: species.composite_key
        }));

        res.json({
            results: formattedResults,
            location,
            pagination: {
                total: totalCount,
                limit,
                offset,
                hasMore: offset + limit < totalCount
            }
        });

    } catch (error) {
        logger.error(`Error fetching species: ${error.message}`);
        res.status(500).json({
            error: "Error fetching species data",
            details: error.message
        });
    }
});

// Route to fetch a single species by its composite key
app.get("/api/species/:compositeKey", async (req, res) => {
    try {
        const { compositeKey } = req.params;

        const query = `
            SELECT *
            FROM species
            WHERE composite_key = $1
        `;

        const result = await pool.query(query, [compositeKey]);

        if (result.rows.length === 0) {
            return res.status(404).json({
                error: "Species not found"
            });
        }

        const species = result.rows[0];

        // Format the response
        const formattedSpecies = {
            key: species.gbif_key,
            scientificName: species.scientific_name,
            vernacularNames: species.vernacular_names || [],
            rank: species.taxonomic_status,
            kingdom: species.kingdom,
            phylum: species.phylum,
            family: species.family,
            class: species.class,
            order: species.order_name,
            genus: species.genus,
            specificEpithet: species.specific_epithet,
            habitat: species.habitat,
            threat_status: species.threat_status,
            description: species.description,
            image: species.image_url || 'placeholder.jpg',
            compositeKey: species.composite_key,
            sound_url: species.sound_url || null,
            location: species.location || null,
            kiswahiliName: species.kiswahili_name || null,
            kinyarwandaName: species.kinyarwanda_name || null
        };

        res.json(formattedSpecies);

    } catch (error) {
        logger.error(`Error fetching species details: ${error.message}`);
        res.status(500).json({
            error: "Error fetching species details",
            details: error.message
        });
    }
});

// Error handling middleware
app.use((err, req, res, next) => {
    logger.error(err.stack);
    res.status(500).json({
        message: "Internal server error",
        error: err.message
    });
});

const PORT = process.env.PORT || 5000;
app.listen(PORT, () => {
    logger.info(`Server running on port ${PORT}`);
    console.log(`Server running on port ${PORT}`);
});