# Biodiversity Awareness Frontend

Frontend application for the Biodiversity Awareness platform, featuring an interactive interface for exploring species data.

## Technology Stack

- React, TypeScript, Tailwind CSS

## Setup

1. Clone the repository
```bash
git clone https://github.com/ekinyua/WildPedia.git
cd frontend
```

2. Install dependencies
```bash
npm install
```

3. Start the development server
```bash
npm run dev
```

4. Build for production
```bash
npm run build
```


## API Integration

The frontend integrates with the backend API:
- Base URL: `/api`
- Endpoints:
  - GET `/api/species?q={query}` - Fetch species data


## License

This project is licensed under the MIT License - see the LICENSE file for details.